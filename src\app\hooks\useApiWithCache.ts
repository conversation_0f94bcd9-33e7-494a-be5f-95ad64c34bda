"use client";

import CacheService, { CacheKeys } from "@/app/services/http/cacheService";
import { useEffect, useState } from "react";

interface UseApiWithCacheOptions<T> {
  cacheKey: CacheKeys | string;
  ttlMinutes?: number;
  fetchFunction: () => Promise<T>;
  enabled?: boolean; // Per controllare quando eseguire la fetch
}

interface UseApiWithCacheResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  clearCache: () => void;
}

export function useApiWithCache<T>({
  cacheKey,
  ttlMinutes = 1,
  fetchFunction,
  enabled = true,
}: UseApiWithCacheOptions<T>): UseApiWithCacheResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);

      const result = await CacheService.fetchWithCache(
        {
          key: cacheKey,
          ttlMinutes,
        },
        fetchFunction
      );

      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
      console.error(`Error fetching data for ${cacheKey}:`, err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    // Pulisce la cache e ricarica i dati
    CacheService.clearCache(cacheKey);
    await fetchData();
  };

  const clearCache = () => {
    CacheService.clearCache(cacheKey);
  };

  useEffect(() => {
    fetchData();
  }, [enabled, cacheKey]); // Ricarica se enabled o cacheKey cambiano

  return {
    data,
    loading,
    error,
    refetch,
    clearCache,
  };
}

// Hook specifici per ogni API per semplificare l'uso
export function useCoingeckoCoins() {
  return useApiWithCache({
    cacheKey: CacheKeys.COINGECKO_COINS,
    ttlMinutes: 1,
    fetchFunction: async () => {
      const response = await fetch(
        "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=250&page=1&sparkline=false&precision=full"
      );
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}

export function useCoinmarketcapCoins() {
  return useApiWithCache({
    cacheKey: CacheKeys.COINMARKETCAP_COINS,
    ttlMinutes: 1,
    fetchFunction: async () => {
      const response = await fetch("/api/coinmarketcap");
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}

export function useCoingeckoCategories() {
  return useApiWithCache({
    cacheKey: CacheKeys.COINGECKO_CATEGORIES,
    ttlMinutes: 5,
    fetchFunction: async () => {
      const response = await fetch(
        "https://api.coingecko.com/api/v3/coins/categories?order=market_cap_desc"
      );
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}

export function useCoingeckoCompanyHolders() {
  return useApiWithCache({
    cacheKey: CacheKeys.COINGECKO_COMPANY_HOLDERS,
    ttlMinutes: 30,
    fetchFunction: async () => {
      const response = await fetch(
        "https://api.coingecko.com/api/v3/companies/public_treasury/ethereum"
      );
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}

export function useCriptovalutaNews() {
  return useApiWithCache({
    cacheKey: CacheKeys.CRIPTOVALUTA_NEWS,
    ttlMinutes: 2,
    fetchFunction: async () => {
      const response = await fetch("/api/criptovaluta-news");
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}

export function useBitboTreasuries() {
  return useApiWithCache({
    cacheKey: CacheKeys.BITBO_TREASURIES,
    ttlMinutes: 60,
    fetchFunction: async () => {
      console.log(
        "🌐 useBitboTreasuries - Chiamando endpoint API /api/bitbo-treasuries"
      );
      const response = await fetch("/api/bitbo-treasuries");
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      console.log("📦 useBitboTreasuries - Dati ricevuti:", {
        historyLength: data.history?.length,
        historyDates: data.history?.map((h: any) => h.date),
      });
      return data;
    },
  });
}

export function useGoogleSheetsEtf() {
  return useApiWithCache({
    cacheKey: CacheKeys.GOOGLE_SHEETS_ETF,
    ttlMinutes: 5, // Cache per 5 minuti (i dati ETF cambiano meno frequentemente)
    fetchFunction: async () => {
      const response = await fetch("/api/google-sheets-etf");
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      return response.json();
    },
  });
}
