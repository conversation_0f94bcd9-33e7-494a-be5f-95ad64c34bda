"use client";
import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <>
      <div className="m-3 mb-6">
        {/* NEWS */}
        {/* <div className="space-y-3 mt-5">
          <Skeleton className="w-100 rounded-lg">
            <div className="h-4 w-100 rounded-lg bg-default-200" />
          </Skeleton>
          <Skeleton className="w-100 rounded-lg">
            <div className="h-4 w-100 rounded-lg bg-default-200" />
          </Skeleton>
          <Skeleton className="w-1/5 rounded-lg">
            <div className="h-4 w-1/5 rounded-lg bg-default-300" />
          </Skeleton>
        </div> */}
        {/* MARKET OVERVIEW */}
        {/* <div>
          <Skeleton className="min-h-[200px] rounded-xl"></Skeleton>
        </div> */}
      </div>
      {/* DOMINANCE & ALTCOIN INDEX */}
      <div className="mx-3 my-6 grid grid-cols-2 gap-4">
        <Skeleton className="min-h-[102px] rounded-xl"></Skeleton>
        <Skeleton className="min-h-[102px] rounded-xl"></Skeleton>
      </div>
      {/* TOP 5 MARKET CAP */}
      <div className="mx-3 my-6 grid grid-cols-1 gap-4">
        <Skeleton className="min-h-[202px] min-w-[351px] rounded-xl"></Skeleton>
      </div>
      {/* TOP 3 PERFORMANCE*/}
      <div className="mx-3 my-6 grid grid-cols-2 gap-4">
        <Skeleton className="min-h-[94px] rounded-xl"></Skeleton>
        <Skeleton className="min-h-[94px] rounded-xl"></Skeleton>
      </div>

      {/* ETF INFLOWS */}
      <div className="mx-3 my-6 grid grid-cols-2 gap-4">
        <Skeleton className="min-h-[64px] rounded-xl"></Skeleton>
        <Skeleton className="min-h-[64px] rounded-xl"></Skeleton>
      </div>

      {/* CRYPTO TABLE */}
    </>
  );
}
