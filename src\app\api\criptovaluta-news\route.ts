import { NextResponse } from 'next/server';
import { parseStringPromise } from 'xml2js';

const CRIPTOVALUTA_RSS_URL = "https://www.criptovaluta.it/feed";

export async function GET() {
  try {
    console.log("Effettuando una nuova chiamata a Criptovaluta News...");
    
    const response = await fetch(CRIPTOVALUTA_RSS_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const xml = await response.text();
    const result = await parseStringPromise(xml);

    // Mappatura dati RSS
    const items = result.rss.channel[0].item
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .filter((item: any) => !item.category.includes("Analisi On Chain"))
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .map((item: any) => ({
        title: item.title,
        link: item.link,
        pubDate: item.pubDate,
        category: item.category,
        description: item.description,
        content: item.content,
      }));

    return NextResponse.json(items);
  } catch (error) {
    console.error("Errore durante il fetch da Criptovaluta News:", error);
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati Criptovaluta News" },
      { status: 500 }
    );
  }
}
